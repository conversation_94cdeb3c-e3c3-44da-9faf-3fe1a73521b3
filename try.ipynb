pip install unstructured

pip install pi-heif

pip install unstructured pi-heif

pip install "unstructured[all]"

pip install unstructured-inference

pip install pdf2image

pip install pdfminer.psexceptions'

pip install pdfminer.six


from unstructured.documents.elements import Text
from unstructured.partition.pdf import partition_pdf
from unstructured.partition.image import partition_image

# Extract text from a PDF file
pdf_elements = partition_pdf(
    filename=r"C:\Users\<USER>\Desktop\AI\Maihndra\FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)\01. PSW - Part Submission Warrant.pdf"
)
pdf_text = [element.text for element in pdf_elements if isinstance(element, Text)]

print("PDF Text:", pdf_text)

# Extract text from a PNG file
image_elements = partition_image(filename="your_file.png")
image_text = [element.text for element in image_elements if isinstance(element, Text)]

print("Image Text:", image_text)


