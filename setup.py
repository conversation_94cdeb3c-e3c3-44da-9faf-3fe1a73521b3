#!/usr/bin/env python3
"""
Setup script for the Document Extractor.
Installs required dependencies and sets up the environment.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_system_dependencies():
    """Install system-level dependencies."""
    print("\n=== Installing System Dependencies ===")
    
    # Check if Tesseract is installed
    try:
        subprocess.run(["tesseract", "--version"], check=True, capture_output=True)
        print("✅ Tesseract OCR is already installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Tesseract OCR not found")
        print("Please install Tesseract OCR:")
        print("- Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
        print("- macOS: brew install tesseract")
        print("- Ubuntu/Debian: sudo apt-get install tesseract-ocr")
        print("- CentOS/RHEL: sudo yum install tesseract")
        return False
    
    return True


def install_python_dependencies():
    """Install Python dependencies."""
    print("\n=== Installing Python Dependencies ===")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if Path("requirements.txt").exists():
        if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing requirements"):
            return False
    else:
        # Install core dependencies manually
        dependencies = [
            "unstructured[all-docs]",
            "pytesseract",
            "pillow",
            "pandas",
            "numpy",
            "opencv-python",
            "pdf2image",
            "pdfplumber",
            "PyPDF2",
            "tqdm"
        ]
        
        for dep in dependencies:
            if not run_command(f"{sys.executable} -m pip install {dep}", f"Installing {dep}"):
                print(f"⚠️  Failed to install {dep}, continuing with others...")
    
    return True


def test_installation():
    """Test if the installation works."""
    print("\n=== Testing Installation ===")
    
    try:
        # Test imports
        import unstructured
        print("✅ unstructured imported successfully")
        
        import pytesseract
        print("✅ pytesseract imported successfully")
        
        from PIL import Image
        print("✅ PIL imported successfully")
        
        import pandas as pd
        print("✅ pandas imported successfully")
        
        # Test the document extractor
        from document_extractor import DocumentExtractor
        extractor = DocumentExtractor()
        print("✅ DocumentExtractor imported successfully")
        
        print("\n🎉 Installation test passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def create_test_directory():
    """Create a test directory with sample usage."""
    print("\n=== Creating Test Directory ===")
    
    test_dir = Path("test_extraction")
    test_dir.mkdir(exist_ok=True)
    
    # Create a simple test script
    test_script = test_dir / "test_simple.py"
    with open(test_script, 'w') as f:
        f.write("""#!/usr/bin/env python3
# Simple test script for document extraction

import sys
sys.path.append('..')

from document_extractor import DocumentExtractor

def test_extraction():
    extractor = DocumentExtractor(output_dir="test_output")
    
    # Test with current directory
    print("Testing directory extraction...")
    results = extractor.extract_from_directory("..", recursive=False)
    
    print(f"Found {len(results)} files to process")
    for result in results:
        file_name = result.get("file_path", "").split("/")[-1]
        if "error" in result:
            print(f"❌ {file_name}: {result['error']}")
        else:
            text_len = len(result.get("extracted_text", ""))
            print(f"✅ {file_name}: {text_len} characters extracted")
    
    if results:
        extractor.save_results(results, "test_results")
        print("Results saved to test_output/")

if __name__ == "__main__":
    test_extraction()
""")
    
    print(f"✅ Test script created: {test_script}")
    print(f"Run it with: cd {test_dir} && python test_simple.py")


def main():
    """Main setup function."""
    print("Document Extractor Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install system dependencies
    if not install_system_dependencies():
        print("\n⚠️  System dependencies not fully installed")
        print("The script may still work, but OCR functionality might be limited")
    
    # Install Python dependencies
    if not install_python_dependencies():
        print("\n❌ Failed to install Python dependencies")
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("\n❌ Installation test failed")
        sys.exit(1)
    
    # Create test directory
    create_test_directory()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run: python document_extractor.py <your_file_or_directory>")
    print("2. Or run: python example_usage.py")
    print("3. Check the generated output directories for results")
    print("\nFor help: python document_extractor.py")


if __name__ == "__main__":
    main()
