#!/usr/bin/env python3
"""
Document Data Extractor
Extracts text and data from PDFs and images using open-source libraries.
Supports: PDF files, images (PNG, JPG, JPEG, TIFF), and various document formats.
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
import json
import logging

# Core libraries for document processing
try:
    from unstructured.partition.auto import partition
    from unstructured.partition.pdf import partition_pdf
    from unstructured.partition.image import partition_image
    from unstructured.staging.base import dict_to_elements
except ImportError:
    print("Installing unstructured library...")
    os.system("pip install unstructured[all-docs]")
    from unstructured.partition.auto import partition
    from unstructured.partition.pdf import partition_pdf
    from unstructured.partition.image import partition_image

# Additional libraries for enhanced processing
try:
    import pytesseract
    from PIL import Image
except ImportError:
    print("Installing OCR libraries...")
    os.system("pip install pytesseract pillow")
    import pytesseract
    from PIL import Image

try:
    import pandas as pd
except ImportError:
    print("Installing pandas...")
    os.system("pip install pandas")
    import pandas as pd

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DocumentExtractor:
    """
    A comprehensive document data extractor supporting multiple file formats.
    """
    
    def __init__(self, output_dir: str = "extracted_data"):
        """
        Initialize the document extractor.
        
        Args:
            output_dir: Directory to save extracted data
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Supported file extensions
        self.pdf_extensions = {'.pdf'}
        self.image_extensions = {'.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif'}
        self.supported_extensions = self.pdf_extensions | self.image_extensions
        
    def extract_from_file(self, file_path: str) -> Dict[str, Any]:
        """
        Extract data from a single file.
        
        Args:
            file_path: Path to the file to process
            
        Returns:
            Dictionary containing extracted data and metadata
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
            
        if file_path.suffix.lower() not in self.supported_extensions:
            raise ValueError(f"Unsupported file type: {file_path.suffix}")
            
        logger.info(f"Processing file: {file_path}")
        
        try:
            if file_path.suffix.lower() in self.pdf_extensions:
                return self._extract_from_pdf(file_path)
            elif file_path.suffix.lower() in self.image_extensions:
                return self._extract_from_image(file_path)
        except Exception as e:
            logger.error(f"Error processing {file_path}: {str(e)}")
            return {
                "file_path": str(file_path),
                "error": str(e),
                "extracted_text": "",
                "elements": [],
                "metadata": {}
            }
    
    def _extract_from_pdf(self, file_path: Path) -> Dict[str, Any]:
        """Extract data from PDF files using unstructured."""
        try:
            # Use unstructured to partition the PDF
            elements = partition_pdf(
                filename=str(file_path),
                strategy="hi_res",  # High resolution for better accuracy
                infer_table_structure=True,  # Extract table structures
                extract_images_in_pdf=True,  # Extract embedded images
                include_page_breaks=True
            )
            
            # Extract text content
            text_content = "\n".join([str(element) for element in elements])
            
            # Organize elements by type
            elements_by_type = {}
            for element in elements:
                element_type = type(element).__name__
                if element_type not in elements_by_type:
                    elements_by_type[element_type] = []
                elements_by_type[element_type].append({
                    "text": str(element),
                    "metadata": element.metadata.to_dict() if hasattr(element, 'metadata') else {}
                })
            
            return {
                "file_path": str(file_path),
                "file_type": "PDF",
                "extracted_text": text_content,
                "elements_by_type": elements_by_type,
                "total_elements": len(elements),
                "metadata": {
                    "file_size": file_path.stat().st_size,
                    "processing_method": "unstructured_hi_res"
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting from PDF {file_path}: {str(e)}")
            # Fallback to basic extraction
            return self._extract_pdf_fallback(file_path)
    
    def _extract_from_image(self, file_path: Path) -> Dict[str, Any]:
        """Extract data from image files using OCR."""
        try:
            # Use unstructured for image processing
            elements = partition_image(
                filename=str(file_path),
                strategy="hi_res",
                infer_table_structure=True
            )
            
            # Also use pytesseract for additional OCR
            image = Image.open(file_path)
            ocr_text = pytesseract.image_to_string(image)
            
            # Extract text content from unstructured elements
            unstructured_text = "\n".join([str(element) for element in elements])
            
            # Combine both extractions
            combined_text = f"=== Unstructured Extraction ===\n{unstructured_text}\n\n=== OCR Extraction ===\n{ocr_text}"
            
            # Organize elements by type
            elements_by_type = {}
            for element in elements:
                element_type = type(element).__name__
                if element_type not in elements_by_type:
                    elements_by_type[element_type] = []
                elements_by_type[element_type].append({
                    "text": str(element),
                    "metadata": element.metadata.to_dict() if hasattr(element, 'metadata') else {}
                })
            
            return {
                "file_path": str(file_path),
                "file_type": "Image",
                "extracted_text": combined_text,
                "unstructured_text": unstructured_text,
                "ocr_text": ocr_text,
                "elements_by_type": elements_by_type,
                "total_elements": len(elements),
                "metadata": {
                    "file_size": file_path.stat().st_size,
                    "image_size": image.size,
                    "image_mode": image.mode,
                    "processing_method": "unstructured + pytesseract"
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting from image {file_path}: {str(e)}")
            return {
                "file_path": str(file_path),
                "error": str(e),
                "extracted_text": "",
                "elements_by_type": {},
                "metadata": {}
            }
    
    def _extract_pdf_fallback(self, file_path: Path) -> Dict[str, Any]:
        """Fallback PDF extraction method."""
        try:
            # Basic unstructured extraction
            elements = partition(filename=str(file_path))
            text_content = "\n".join([str(element) for element in elements])
            
            return {
                "file_path": str(file_path),
                "file_type": "PDF",
                "extracted_text": text_content,
                "elements": [str(element) for element in elements],
                "total_elements": len(elements),
                "metadata": {
                    "file_size": file_path.stat().st_size,
                    "processing_method": "unstructured_basic"
                }
            }
        except Exception as e:
            return {
                "file_path": str(file_path),
                "error": str(e),
                "extracted_text": "",
                "elements": [],
                "metadata": {}
            }
    
    def extract_from_directory(self, directory_path: str, recursive: bool = True) -> List[Dict[str, Any]]:
        """
        Extract data from all supported files in a directory.
        
        Args:
            directory_path: Path to the directory
            recursive: Whether to search subdirectories
            
        Returns:
            List of extraction results
        """
        directory_path = Path(directory_path)
        
        if not directory_path.exists():
            raise FileNotFoundError(f"Directory not found: {directory_path}")
            
        results = []
        
        # Find all supported files
        if recursive:
            files = [f for f in directory_path.rglob("*") if f.is_file() and f.suffix.lower() in self.supported_extensions]
        else:
            files = [f for f in directory_path.iterdir() if f.is_file() and f.suffix.lower() in self.supported_extensions]
        
        logger.info(f"Found {len(files)} supported files in {directory_path}")
        
        for file_path in files:
            result = self.extract_from_file(file_path)
            results.append(result)
            
        return results
    
    def save_results(self, results: List[Dict[str, Any]], output_filename: str = "extraction_results"):
        """
        Save extraction results to files.
        
        Args:
            results: List of extraction results
            output_filename: Base filename for output files
        """
        # Save as JSON
        json_path = self.output_dir / f"{output_filename}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        logger.info(f"Results saved to: {json_path}")
        
        # Save as CSV summary
        csv_data = []
        for result in results:
            csv_data.append({
                "file_path": result.get("file_path", ""),
                "file_type": result.get("file_type", ""),
                "text_length": len(result.get("extracted_text", "")),
                "total_elements": result.get("total_elements", 0),
                "has_error": "error" in result,
                "processing_method": result.get("metadata", {}).get("processing_method", "")
            })
        
        df = pd.DataFrame(csv_data)
        csv_path = self.output_dir / f"{output_filename}_summary.csv"
        df.to_csv(csv_path, index=False)
        logger.info(f"Summary saved to: {csv_path}")
        
        # Save individual text files
        text_dir = self.output_dir / "extracted_texts"
        text_dir.mkdir(exist_ok=True)
        
        for i, result in enumerate(results):
            if result.get("extracted_text"):
                file_path = Path(result["file_path"])
                text_filename = f"{file_path.stem}_{i}.txt"
                text_path = text_dir / text_filename
                
                with open(text_path, 'w', encoding='utf-8') as f:
                    f.write(f"Source: {result['file_path']}\n")
                    f.write(f"Type: {result.get('file_type', 'Unknown')}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(result["extracted_text"])
                
        logger.info(f"Individual text files saved to: {text_dir}")


def main():
    """Main function for command-line usage."""
    if len(sys.argv) < 2:
        print("Usage: python document_extractor.py <file_or_directory_path>")
        print("Example: python document_extractor.py ./my_documents")
        print("Example: python document_extractor.py ./document.pdf")
        return
    
    input_path = sys.argv[1]
    extractor = DocumentExtractor()
    
    try:
        if Path(input_path).is_file():
            # Extract from single file
            result = extractor.extract_from_file(input_path)
            results = [result]
        else:
            # Extract from directory
            results = extractor.extract_from_directory(input_path)
        
        # Save results
        extractor.save_results(results)
        
        # Print summary
        print(f"\nExtraction completed!")
        print(f"Processed {len(results)} files")
        print(f"Results saved to: {extractor.output_dir}")
        
        # Print brief summary
        for result in results:
            file_path = result.get("file_path", "Unknown")
            text_length = len(result.get("extracted_text", ""))
            if "error" in result:
                print(f"❌ {file_path}: Error - {result['error']}")
            else:
                print(f"✅ {file_path}: Extracted {text_length} characters")
                
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        print(f"Error: {str(e)}")


if __name__ == "__main__":
    main()
