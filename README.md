# Document Data Extractor

A comprehensive Python solution for extracting text and data from PDFs and images using open-source libraries.

## Features

- **PDF Extraction**: High-resolution text extraction with table structure detection
- **Image OCR**: Extract text from images using multiple OCR engines
- **Batch Processing**: Process entire directories of documents
- **Multiple Output Formats**: JSON, CSV, and individual text files
- **Error Handling**: Robust error handling with fallback methods
- **Metadata Extraction**: File information and processing details

## Supported File Types

- **PDFs**: `.pdf`
- **Images**: `.png`, `.jpg`, `.jpeg`, `.tiff`, `.bmp`, `.gif`

## Quick Start

### 1. Setup

```bash
# Run the setup script to install dependencies
python setup.py
```

### 2. Basic Usage

```bash
# Extract from a single file
python document_extractor.py "path/to/your/file.pdf"

# Extract from a directory
python document_extractor.py "path/to/your/directory"

# Run examples
python example_usage.py
```

### 3. Programmatic Usage

```python
from document_extractor import DocumentExtractor

# Create extractor instance
extractor = DocumentExtractor(output_dir="my_output")

# Extract from single file
result = extractor.extract_from_file("document.pdf")

# Extract from directory
results = extractor.extract_from_directory("./documents")

# Save results
extractor.save_results(results, "my_extraction")
```

## Libraries Used

- **[Unstructured](https://github.com/Unstructured-IO/unstructured)**: Primary document processing library
- **[Pytesseract](https://github.com/madmaze/pytesseract)**: OCR for images
- **[PIL/Pillow](https://pillow.readthedocs.io/)**: Image processing
- **[Pandas](https://pandas.pydata.org/)**: Data handling and CSV export

## Output Structure

The extractor creates the following output structure:

```
extracted_data/
├── extraction_results.json          # Complete extraction data
├── extraction_results_summary.csv   # Summary statistics
└── extracted_texts/                 # Individual text files
    ├── document1_0.txt
    ├── document2_1.txt
    └── ...
```

## Example Output

### JSON Structure
```json
{
  "file_path": "document.pdf",
  "file_type": "PDF",
  "extracted_text": "Full extracted text...",
  "elements_by_type": {
    "Title": [...],
    "NarrativeText": [...],
    "Table": [...]
  },
  "total_elements": 25,
  "metadata": {
    "file_size": 1024000,
    "processing_method": "unstructured_hi_res"
  }
}
```

## Advanced Usage

### Custom Processing Options

```python
# Create extractor with custom output directory
extractor = DocumentExtractor(output_dir="custom_output")

# Process with error handling
try:
    result = extractor.extract_from_file("complex_document.pdf")
    if "error" not in result:
        print(f"Extracted {len(result['extracted_text'])} characters")
    else:
        print(f"Error: {result['error']}")
except Exception as e:
    print(f"Processing failed: {e}")
```

### Batch Processing with Progress

```python
from pathlib import Path
from tqdm import tqdm

extractor = DocumentExtractor()
files = list(Path("documents").glob("*.pdf"))

results = []
for file_path in tqdm(files, desc="Processing documents"):
    result = extractor.extract_from_file(file_path)
    results.append(result)

extractor.save_results(results, "batch_processing")
```

## System Requirements

- Python 3.8 or higher
- Tesseract OCR (for image processing)

### Installing Tesseract

- **Windows**: Download from [GitHub releases](https://github.com/UB-Mannheim/tesseract/wiki)
- **macOS**: `brew install tesseract`
- **Ubuntu/Debian**: `sudo apt-get install tesseract-ocr`
- **CentOS/RHEL**: `sudo yum install tesseract`

## Troubleshooting

### Common Issues

1. **Tesseract not found**
   - Install Tesseract OCR for your system
   - Add Tesseract to your system PATH

2. **Memory issues with large PDFs**
   - Process files individually instead of batch processing
   - Use the fallback extraction method

3. **Poor OCR quality**
   - Ensure images are high resolution
   - Preprocess images (contrast, brightness) if needed

### Error Handling

The extractor includes comprehensive error handling:
- Files that fail to process will have an "error" field in the results
- Fallback methods are used when primary extraction fails
- Processing continues even if individual files fail

## Performance Tips

1. **For large directories**: Use `recursive=False` to process only the target directory
2. **For large files**: Process individually rather than in batches
3. **For images**: Ensure good quality and resolution for better OCR results

## License

This project uses open-source libraries. Check individual library licenses for details.

## Contributing

Feel free to submit issues and enhancement requests!
