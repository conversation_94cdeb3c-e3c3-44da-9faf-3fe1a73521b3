#!/usr/bin/env python3
"""
Example usage of the DocumentExtractor class.
Shows how to extract data from PDFs and images.
"""

from document_extractor import DocumentExtractor
import json
from pathlib import Path


def example_single_file():
    """Example: Extract data from a single file."""
    print("=== Single File Extraction Example ===")
    
    extractor = DocumentExtractor(output_dir="single_file_output")
    
    # Example with a PDF file (replace with your actual file path)
    file_path = "./FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf"
    
    if Path(file_path).exists():
        result = extractor.extract_from_file(file_path)
        
        print(f"File: {result['file_path']}")
        print(f"Type: {result.get('file_type', 'Unknown')}")
        print(f"Text length: {len(result.get('extracted_text', ''))}")
        print(f"Elements found: {result.get('total_elements', 0)}")
        
        # Save the result
        extractor.save_results([result], "single_file_result")
        
        # Print first 500 characters of extracted text
        text = result.get('extracted_text', '')
        if text:
            print(f"\nFirst 500 characters of extracted text:")
            print("-" * 50)
            print(text[:500] + "..." if len(text) > 500 else text)
    else:
        print(f"File not found: {file_path}")


def example_directory_extraction():
    """Example: Extract data from all files in a directory."""
    print("\n=== Directory Extraction Example ===")
    
    extractor = DocumentExtractor(output_dir="directory_output")
    
    # Extract from the current directory
    directory_path = "."
    
    results = extractor.extract_from_directory(directory_path, recursive=True)
    
    print(f"Processed {len(results)} files")
    
    # Save all results
    extractor.save_results(results, "directory_extraction")
    
    # Print summary
    for result in results:
        file_path = result.get("file_path", "Unknown")
        text_length = len(result.get("extracted_text", ""))
        if "error" in result:
            print(f"❌ {Path(file_path).name}: Error - {result['error']}")
        else:
            print(f"✅ {Path(file_path).name}: {text_length} characters extracted")


def example_image_extraction():
    """Example: Extract data from image files."""
    print("\n=== Image Extraction Example ===")
    
    extractor = DocumentExtractor(output_dir="image_output")
    
    # Look for image files in current directory
    image_files = [
        "./Screenshot 2025-07-12 104359.png",
        "./Screenshot 2025-07-12 104415.png",
        "./Screenshot 2025-07-12 104430.png",
        "./Screenshot 2025-07-12 104445.png"
    ]
    
    results = []
    for image_path in image_files:
        if Path(image_path).exists():
            print(f"Processing: {image_path}")
            result = extractor.extract_from_file(image_path)
            results.append(result)
            
            # Show brief summary
            text_length = len(result.get("extracted_text", ""))
            print(f"  Extracted {text_length} characters")
    
    if results:
        extractor.save_results(results, "image_extraction")
        print(f"Processed {len(results)} image files")


def example_custom_processing():
    """Example: Custom processing with specific options."""
    print("\n=== Custom Processing Example ===")
    
    extractor = DocumentExtractor(output_dir="custom_output")
    
    # Process a specific PDF with detailed analysis
    pdf_path = "./FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/PFD - P80009246A.pdf"
    
    if Path(pdf_path).exists():
        result = extractor.extract_from_file(pdf_path)
        
        # Analyze the elements by type
        elements_by_type = result.get('elements_by_type', {})
        
        print(f"File: {Path(pdf_path).name}")
        print("Element types found:")
        for element_type, elements in elements_by_type.items():
            print(f"  {element_type}: {len(elements)} items")
            
            # Show first element of each type
            if elements:
                first_element = elements[0]
                text_preview = first_element['text'][:100] + "..." if len(first_element['text']) > 100 else first_element['text']
                print(f"    Example: {text_preview}")
        
        # Save with custom filename
        extractor.save_results([result], f"custom_{Path(pdf_path).stem}")


def main():
    """Run all examples."""
    print("Document Extractor Examples")
    print("=" * 50)
    
    try:
        # Run examples
        example_single_file()
        example_directory_extraction()
        example_image_extraction()
        example_custom_processing()
        
        print("\n" + "=" * 50)
        print("All examples completed!")
        print("Check the output directories for extracted data:")
        print("- single_file_output/")
        print("- directory_output/")
        print("- image_output/")
        print("- custom_output/")
        
    except Exception as e:
        print(f"Error running examples: {str(e)}")
        print("Make sure you have installed the required dependencies:")
        print("pip install -r requirements.txt")


if __name__ == "__main__":
    main()
